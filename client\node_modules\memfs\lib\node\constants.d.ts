export declare const enum MODE {
    FILE = 438,
    DIR = 511,
    DEFAULT = 438
}
export declare const ERRSTR: {
    PATH_STR: string;
    FD: string;
    MODE_INT: string;
    CB: string;
    UID: string;
    GID: string;
    LEN: string;
    ATIME: string;
    MTIME: string;
    PREFIX: string;
    BUFFER: string;
    OFFSET: string;
    LENGTH: string;
    POSITION: string;
};
export declare enum FLAGS {
    r,
    'r+',
    rs,
    sr,
    'rs+',
    'sr+',
    w,
    wx,
    xw,
    'w+',
    'wx+',
    'xw+',
    a,
    ax,
    xa,
    'a+',
    'ax+',
    'xa+'
}

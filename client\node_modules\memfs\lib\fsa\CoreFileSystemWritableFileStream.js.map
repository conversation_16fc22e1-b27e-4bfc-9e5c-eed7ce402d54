{"version": 3, "file": "CoreFileSystemWritableFileStream.js", "sourceRoot": "", "sources": ["../../src/fsa/CoreFileSystemWritableFileStream.ts"], "names": [], "mappings": ";;;AAEA,2DAAwD;AAExD,iCAA4C;AAC5C,iDAAgD;AAGhD,MAAM,EAAE,GAAG,CACT,OAAO,cAAc,KAAK,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,cAAc,CACrE,CAAC;AAE3B;;GAEG;AACH,MAAa,gCAAiC,SAAQ,EAAE;IAOtD,YAAY,IAAgB,EAAE,IAAY,EAAE,mBAA4B,KAAK;QAC3E,IAAI,EAAsB,CAAC;QAE3B,KAAK,CAAC;YACJ,KAAK,EAAE,UAAU,CAAC,EAAE;gBAClB,wBAAwB;gBACxB,MAAM,KAAK,GAAG,gBAAgB,CAAC,CAAC,CAAC,iBAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,iBAAK,CAAC,CAAC,CAAC;gBACvD,IAAI,CAAC;oBACH,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,sBAAY,CAAC;gBACzC,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,IAAI,qCAAsB,EAAE,CAAC;wBAC3E,MAAM,IAAA,yBAAkB,GAAE,CAAC;oBAC7B,CAAC;oBACD,MAAM,KAAK,CAAC;gBACd,CAAC;YACH,CAAC;YACD,KAAK,EAAE,KAAK,EAAE,KAAgD,EAAE,EAAE;gBAChE,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC3B,CAAC;YACD,KAAK,EAAE,KAAK,IAAI,EAAE;gBAChB,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,GAAG,KAAK,SAAS,EAAE,CAAC;oBAC5C,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;oBACrB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;gBACtB,CAAC;YACH,CAAC;YACD,KAAK,EAAE,KAAK,IAAI,EAAE;gBAChB,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,GAAG,KAAK,SAAS,EAAE,CAAC;oBAC5C,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;oBACrB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;gBACtB,CAAC;YACH,CAAC;SACF,CAAC,CAAC;QApCG,cAAS,GAAW,CAAC,CAAC;QACtB,YAAO,GAAG,KAAK,CAAC;QAqCtB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,IAAI,CAAC,QAAgB;QAChC,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,MAAM,IAAI,YAAY,CAAC,uBAAuB,EAAE,mBAAmB,CAAC,CAAC;QACvE,CAAC;QACD,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;IAC5B,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,QAAQ,CAAC,IAAY;QAChC,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,MAAM,IAAI,YAAY,CAAC,uBAAuB,EAAE,mBAAmB,CAAC,CAAC;QACvE,CAAC;QACD,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC3D,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;YAC5B,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QACtB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,IAAI,qCAAsB,EAAE,CAAC;gBAC3E,MAAM,IAAA,yBAAkB,GAAE,CAAC;YAC7B,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAOM,KAAK,CAAC,KAAK,CAAC,aAAwD;QACzE,MAAM,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;IACnC,CAAC;IAEO,KAAK,CAAC,MAAM,CAAC,aAAwD;QAC3E,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,MAAM,IAAI,YAAY,CAAC,uBAAuB,EAAE,mBAAmB,CAAC,CAAC;QACvE,CAAC;QAED,IAAI,IAAI,CAAC,GAAG,KAAK,SAAS,EAAE,CAAC;YAC3B,MAAM,IAAI,YAAY,CAAC,0BAA0B,EAAE,mBAAmB,CAAC,CAAC;QAC1E,CAAC;QAED,IAAI,CAAC;YACH,IAAI,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,EAAE,CAAC;gBAClC,MAAM,MAAM,GAAG,aAAa,CAAC;gBAC7B,QAAQ,MAAM,CAAC,IAAI,EAAE,CAAC;oBACpB,KAAK,OAAO,CAAC,CAAC,CAAC;wBACb,IAAI,MAAM,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;4BAC9B,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;4BAC/C,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC;4BAClF,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;4BAC/E,IAAI,MAAM,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;gCAClC,IAAI,CAAC,SAAS,IAAI,OAAO,CAAC;4BAC5B,CAAC;wBACH,CAAC;wBACD,MAAM;oBACR,CAAC;oBACD,KAAK,MAAM,CAAC,CAAC,CAAC;wBACZ,IAAI,MAAM,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;4BAClC,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;wBACnC,CAAC;wBACD,MAAM;oBACR,CAAC;oBACD,KAAK,UAAU,CAAC,CAAC,CAAC;wBAChB,IAAI,MAAM,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;4BAC9B,MAAM,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;wBACnC,CAAC;wBACD,MAAM;oBACR,CAAC;gBACH,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,oBAAoB;gBACpB,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;gBACjD,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;gBACrF,IAAI,CAAC,SAAS,IAAI,OAAO,CAAC;YAC5B,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,IAAI,qCAAsB,EAAE,CAAC;gBAC3E,MAAM,IAAA,yBAAkB,GAAE,CAAC;YAC7B,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,SAAS,CAAC,KAAgD;QAChE,OAAO,CAAC,CAAC,CAAC,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,MAAM,IAAI,KAAK,CAAC,CAAC;IACnE,CAAC;IAEO,aAAa,CAAC,IAAU;QAC9B,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;YAC7B,OAAO,eAAM,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QACnC,CAAC;QACD,IAAI,IAAI,YAAY,eAAM,EAAE,CAAC;YAC3B,OAAO,IAAI,CAAC;QACd,CAAC;QACD,IAAI,IAAI,YAAY,WAAW,EAAE,CAAC;YAChC,OAAO,eAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3B,CAAC;QACD,IAAI,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;YAC7B,OAAO,eAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QACpE,CAAC;QACD,IAAI,IAAI,YAAY,IAAI,EAAE,CAAC;YACzB,oDAAoD;YACpD,sCAAsC;YACtC,MAAM,IAAI,KAAK,CAAC,2DAA2D,CAAC,CAAC;QAC/E,CAAC;QACD,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;IAC3C,CAAC;CACF;AA9JD,4EA8JC"}
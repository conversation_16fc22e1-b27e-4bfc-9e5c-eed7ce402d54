{"version": 3, "file": "CoreFileSystemDirectoryHandle.js", "sourceRoot": "", "sources": ["../../src/fsa/CoreFileSystemDirectoryHandle.ts"], "names": [], "mappings": ";;;AAAA,iEAA8D;AAC9D,iCAQgB;AAChB,yEAAsE;AAatE,2DAAwD;AACxD,iDAAgD;AAEhD;;GAEG;AACH,MAAa,6BAA8B,SAAQ,2CAAoB;IAKrE,YACqB,KAAiB,EACpC,IAAY,EACZ,MAA+B,EAAE;QAEjC,MAAM,OAAO,GAAG,IAAA,UAAS,EAAC,GAAG,CAAC,CAAC;QAC/B,KAAK,CAAC,WAAW,EAAE,IAAA,eAAQ,EAAC,IAAI,EAAE,OAAO,CAAC,SAAS,CAAC,EAAE,OAAO,CAAC,CAAC;QAL5C,UAAK,GAAL,KAAK,CAAY;QAMpC,IAAI,CAAC,GAAG,GAAG,OAAO,CAAC;QACnB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC;IAChG,CAAC;IAED;;;;;OAKG;IACI,KAAK,CAAC,CAAC,IAAI;QAChB,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC5D,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;YAC/B,KAAK,MAAM,CAAC,IAAI,CAAC,IAAI,QAAQ,EAAE,CAAC;gBAC9B,IAAI,IAAI,KAAK,GAAG,IAAI,IAAI,KAAK,IAAI,EAAE,CAAC;oBAClC,MAAM,IAAI,CAAC;gBACb,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;QACnC,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,CAAC,OAAO;QACnB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;QAC1C,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;YAChD,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;YAC/B,KAAK,MAAM,CAAC,IAAI,EAAE,SAAS,CAAC,IAAI,QAAQ,EAAE,CAAC;gBACzC,IAAI,IAAI,KAAK,GAAG,IAAI,IAAI,KAAK,IAAI,IAAI,SAAS,EAAE,CAAC;oBAC/C,MAAM,SAAS,GAAG,IAAI,GAAG,IAAI,CAAC;oBAC9B,MAAM,IAAI,GAAG,SAAS,CAAC,OAAO,EAAE,CAAC;oBACjC,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;wBACvB,MAAM,CAAC,IAAI,EAAE,IAAI,6BAA6B,CAAC,KAAK,EAAE,SAAS,EAAE,GAAG,CAAC,CAAC,CAAC;oBACzE,CAAC;yBAAM,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC;wBACzB,MAAM,CAAC,IAAI,EAAE,IAAI,mDAAwB,CAAC,KAAK,EAAE,SAAS,EAAE,GAAG,CAAC,CAAC,CAAC;oBACpE,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;QACtC,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACI,KAAK,CAAC,CAAC,MAAM;QAClB,IAAI,KAAK,EAAE,MAAM,CAAC,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,OAAO,EAAE;YAAE,MAAM,KAAK,CAAC;IAC5D,CAAC;IAED;;;;;;;;;OASG;IACI,KAAK,CAAC,kBAAkB,CAC7B,IAAY,EACZ,OAAmC;QAEnC,IAAA,iBAAU,EAAC,IAAI,EAAE,oBAAoB,EAAE,2BAA2B,CAAC,CAAC;QACpE,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACpC,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;YAClD,IAAI,IAAI,EAAE,CAAC;gBACT,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;gBAC5B,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;oBAAE,MAAM,IAAA,2BAAoB,GAAE,CAAC;gBACtD,OAAO,IAAI,6BAA6B,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;YAC3E,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,kDAAkD;YAC/E,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,YAAY;gBAAE,MAAM,KAAK,CAAC;YAC/C,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;gBACvC,IAAI,KAAK,CAAC,IAAI,qCAAsB,IAAI,KAAK,CAAC,OAAO,KAAK,QAAQ,EAAE,CAAC;oBACnE,IAAI,OAAO,EAAE,MAAM,EAAE,CAAC;wBACpB,IAAA,qBAAc,EAAC,IAAI,CAAC,GAAG,CAAC,IAAK,CAAC,CAAC;wBAC/B,IAAI,CAAC;4BACH,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;4BAClC,OAAO,IAAI,6BAA6B,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;wBAC3E,CAAC;wBAAC,OAAO,WAAW,EAAE,CAAC;4BACrB,IAAI,WAAW,IAAI,OAAO,WAAW,KAAK,QAAQ,IAAI,WAAW,CAAC,IAAI,qCAAsB,EAAE,CAAC;gCAC7F,MAAM,IAAA,yBAAkB,GAAE,CAAC;4BAC7B,CAAC;4BACD,MAAM,WAAW,CAAC;wBACpB,CAAC;oBACH,CAAC;oBACD,MAAM,IAAA,uBAAgB,GAAE,CAAC;gBAC3B,CAAC;gBACD,IAAI,KAAK,CAAC,IAAI,qCAAsB,EAAE,CAAC;oBACrC,MAAM,IAAA,yBAAkB,GAAE,CAAC;gBAC7B,CAAC;YACH,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;;;;;OAQG;IACI,KAAK,CAAC,aAAa,CAAC,IAAY,EAAE,OAA8B;QACrE,IAAA,iBAAU,EAAC,IAAI,EAAE,eAAe,EAAE,2BAA2B,CAAC,CAAC;QAC/D,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACpC,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;YAClD,IAAI,IAAI,EAAE,CAAC;gBACT,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;gBAC5B,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;oBAAE,MAAM,IAAA,2BAAoB,GAAE,CAAC;gBACjD,OAAO,IAAI,mDAAwB,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;YACtE,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,kDAAkD;YAC/E,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,YAAY;gBAAE,MAAM,KAAK,CAAC;YAC/C,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;gBACvC,IAAI,KAAK,CAAC,IAAI,qCAAsB,IAAI,KAAK,CAAC,OAAO,KAAK,QAAQ,EAAE,CAAC;oBACnE,IAAI,OAAO,EAAE,MAAM,EAAE,CAAC;wBACpB,IAAA,qBAAc,EAAC,IAAI,CAAC,GAAG,CAAC,IAAK,CAAC,CAAC;wBAC/B,IAAI,CAAC;4BACH,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,QAAQ,EAAE,eAAM,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,iBAAK,CAAC,CAAC,sBAAY,CAAC;4BACpE,OAAO,IAAI,mDAAwB,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;wBACtE,CAAC;wBAAC,OAAO,WAAW,EAAE,CAAC;4BACrB,IAAI,WAAW,IAAI,OAAO,WAAW,KAAK,QAAQ,IAAI,WAAW,CAAC,IAAI,qCAAsB,EAAE,CAAC;gCAC7F,MAAM,IAAA,yBAAkB,GAAE,CAAC;4BAC7B,CAAC;4BACD,MAAM,WAAW,CAAC;wBACpB,CAAC;oBACH,CAAC;oBACD,MAAM,IAAA,uBAAgB,GAAE,CAAC;gBAC3B,CAAC;gBACD,IAAI,KAAK,CAAC,IAAI,qCAAsB,EAAE,CAAC;oBACrC,MAAM,IAAA,yBAAkB,GAAE,CAAC;gBAC7B,CAAC;YACH,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;;;;;OAQG;IACI,KAAK,CAAC,WAAW,CAAC,IAAY,EAAE,EAAE,SAAS,GAAG,KAAK,KAAyB,EAAE;QACnF,IAAA,qBAAc,EAAC,IAAI,CAAC,GAAG,CAAC,IAAK,CAAC,CAAC;QAC/B,IAAA,iBAAU,EAAC,IAAI,EAAE,aAAa,EAAE,2BAA2B,CAAC,CAAC;QAC7D,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACpC,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC;YACzD,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;YAC5B,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC;gBAClB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAC9B,CAAC;iBAAM,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;gBAC9B,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;YACxC,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAA,2BAAoB,GAAE,CAAC;YAC/B,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,YAAY;gBAAE,MAAM,KAAK,CAAC;YAC/C,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;gBACvC,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;oBACnB,qCAAsB,CAAC,CAAC,CAAC;wBACvB,MAAM,IAAA,uBAAgB,GAAE,CAAC;oBAC3B,CAAC;oBACD;wBACE,MAAM,IAAA,yBAAkB,GAAE,CAAC;oBAC7B;wBACE,MAAM,IAAI,YAAY,CAAC,6CAA6C,EAAE,0BAA0B,CAAC,CAAC;gBACtG,CAAC;YACH,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;;;;;OAQG;IACI,KAAK,CAAC,OAAO,CAAC,kBAAqC;QACxD,IACE,kBAAkB,YAAY,6BAA6B;YAC3D,kBAAkB,YAAY,mDAAwB,EACtD,CAAC;YACD,sDAAsD;YACtD,IAAK,kBAA0B,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK;gBAAE,OAAO,IAAI,CAAC;YAElE,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC;YACzB,MAAM,SAAS,GAAG,kBAAkB,CAAC,MAAM,CAAC;YAC5C,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC;gBAAE,OAAO,IAAI,CAAC;YAC7C,IAAI,QAAQ,GAAG,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC5C,IAAI,QAAQ,KAAK,EAAE;gBAAE,OAAO,EAAE,CAAC;YAC/B,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,SAAU,CAAC;YACtC,IAAI,QAAQ,CAAC,CAAC,CAAC,KAAK,SAAS;gBAAE,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAC5D,OAAO,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QACnC,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,YAAY,CAAC,KAAU,EAAE,MAAc;QAC7C,IAAI,KAAK,YAAY,YAAY;YAAE,MAAM,KAAK,CAAC;QAC/C,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YACvC,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;gBACnB;oBACE,MAAM,IAAA,uBAAgB,GAAE,CAAC;gBAC3B;oBACE,MAAM,IAAA,yBAAkB,GAAE,CAAC;YAC/B,CAAC;QACH,CAAC;QACD,MAAM,KAAK,CAAC;IACd,CAAC;CACF;AAzPD,sEAyPC"}
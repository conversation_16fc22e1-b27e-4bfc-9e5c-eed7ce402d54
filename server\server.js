const express = require('express')
const http = require('http')
const socketIo = require('socket.io')
const cors = require('cors')

const app = express()
const server = http.createServer(app)
const io = socketIo(server, {
    cors: {
        origin: "*",
        methods: ["GET", "POST"]
    }
})

const port = 3000

app.use(cors())
app.use(express.static('public'))

// Store rooms and participants
const rooms = new Map()

io.on('connection', (socket) => {
    console.log('User connected:', socket.id)
    let currentRoomId = null

    socket.on('join-room', (roomId, userId) => {
        socket.join(roomId)
        currentRoomId = roomId

        if (!rooms.has(roomId)) {
            rooms.set(roomId, new Set())
        }
        rooms.get(roomId).add(userId)

        socket.to(roomId).emit('user-connected', userId)
        console.log(`User ${userId} joined room ${roomId}`)

        socket.on('disconnect', () => {
            socket.to(roomId).emit('user-disconnected', userId)
            if (rooms.has(roomId)) {
                rooms.get(roomId).delete(userId)
                if (rooms.get(roomId).size === 0) {
                    rooms.delete(roomId)
                }
            }
            console.log(`User ${userId} disconnected from room ${roomId}`)
        })
    })

    // WebRTC signaling
    socket.on('offer', (data) => {
        socket.to(currentRoomId).emit('offer', data)
    })

    socket.on('answer', (data) => {
        socket.to(currentRoomId).emit('answer', data)
    })

    socket.on('ice-candidate', (data) => {
        socket.to(currentRoomId).emit('ice-candidate', data)
    })

    socket.on('toggle-video', (data) => {
        socket.to(currentRoomId).emit('user-toggle-video', data)
    })

    socket.on('toggle-audio', (data) => {
        socket.to(currentRoomId).emit('user-toggle-audio', data)
    })
})

app.get('/', (_req, res) => {
    res.send('Video Call Server Running!')
})

server.listen(port, () => {
    console.log(`Video call server listening on port ${port}`)
})

{"version": 3, "file": "errors.js", "sourceRoot": "", "sources": ["../../../../src/vendor/node/internal/errors.ts"], "names": [], "mappings": ";AAAA,2FAA2F;;;AA2ElF,0BAAO;AAAkB,cAAC;AAzEnC,kCAA0C;AAE1C,MAAM,KAAK,GAAG,OAAO,MAAM,KAAK,WAAW,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAE,MAAc,CAAC,MAAM,CAAC,CAAC;AACjF,MAAM,QAAQ,GAAG,EAAE,CAAC;AAEpB,SAAS,aAAa,CAAC,IAAI;IACzB,OAAO,MAAM,SAAU,SAAQ,IAAI;QACjC,YAAY,GAAG,EAAE,GAAG,IAAI;YACtB,KAAK,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC;YAC1B,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;YAChB,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC;YAClB,IAAI,CAAC,IAAI,GAAG,GAAG,KAAK,CAAC,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;QAC/C,CAAC;KACF,CAAC;AACJ,CAAC;AAED,MAAM,CAAC,GAAG,OAAO,UAAU,KAAK,WAAW,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC;AAElE,MAAM,cAAe,SAAQ,CAAC,CAAC,KAAK;IAQlC,YAAY,OAAO;QACjB,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,OAAO,KAAK,IAAI,EAAE,CAAC;YACpD,MAAM,IAAI,OAAO,CAAC,SAAS,CAAC,sBAAsB,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;QAC3E,CAAC;QACD,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;YACpB,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QACzB,CAAC;aAAM,CAAC;YACN,KAAK,CACH,GAAG,IAAA,cAAO,EAAC,OAAO,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,QAAQ,IAAI,IAAA,cAAO,EAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAC/G,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,gBAAgB,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC;QACzC,IAAI,CAAC,IAAI,GAAG,gCAAgC,CAAC;QAC7C,IAAI,CAAC,IAAI,GAAG,eAAe,CAAC;QAC5B,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;QAC7B,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;QACjC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;QACjC,aAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,OAAO,CAAC,kBAAkB,CAAC,CAAC;IAC5D,CAAC;CACF;AA2BiB,wCAAc;AAzBhC,SAAS,OAAO,CAAC,GAAG,EAAE,IAAI;IACxB,IAAI,OAAO,GAAG,KAAK,QAAQ;QAAE,MAAM,IAAI,aAAK,CAAC,oCAAoC,CAAC,CAAC;IACnF,MAAM,GAAG,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC;IAC1B,IAAI,CAAC,GAAG;QAAE,MAAM,IAAI,aAAK,CAAC,0CAA0C,GAAG,GAAG,CAAC,CAAC;IAC5E,IAAI,GAAG,CAAC;IACR,IAAI,OAAO,GAAG,KAAK,UAAU,EAAE,CAAC;QAC9B,GAAG,GAAG,GAAG,CAAC;IACZ,CAAC;SAAM,CAAC;QACN,GAAG,GAAG,aAAM,CAAC;QACb,IAAI,IAAI,KAAK,SAAS,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,GAAG,CAAC;QACxD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IACpB,CAAC;IACD,OAAO,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;AACvC,CAAC;AAED,6EAA6E;AAC7E,+BAA+B;AAC/B,SAAS,CAAC,CAAC,GAAG,EAAE,GAAG;IACjB,QAAQ,CAAC,GAAG,CAAC,GAAG,OAAO,GAAG,KAAK,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AAChE,CAAC;AAEY,QAAA,KAAK,GAAG,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;AAC/B,QAAA,SAAS,GAAG,aAAa,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;AACvC,QAAA,UAAU,GAAG,aAAa,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;AAItD,CAAC,CAAC,gBAAgB,EAAE,6BAA6B,CAAC,CAAC;AACnD,CAAC,CACC,8BAA8B,EAC9B,wFAAwF,CACzF,CAAC;AACF,CAAC,CAAC,2BAA2B,EAAE,kDAAkD,CAAC,CAAC;AACnF,CAAC,CAAC,2BAA2B,EAAE,kBAAkB,CAAC,CAAC;AACnD,CAAC,CAAC,uBAAuB,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;IACzC,OAAO,cAAc,MAAM,CAAC,KAAK,CAAC,4BAA4B,IAAI,GAAG,CAAC;AACxE,CAAC,CAAC,CAAC;AACH,CAAC,CAAC,gCAAgC,EAAE,KAAK,CAAC,EAAE,CAAC,cAAc,MAAM,CAAC,KAAK,CAAC,oCAAoC,CAAC,CAAC;AAC9G,CAAC,CAAC,uBAAuB,EAAE,6BAA6B,CAAC,CAAC"}
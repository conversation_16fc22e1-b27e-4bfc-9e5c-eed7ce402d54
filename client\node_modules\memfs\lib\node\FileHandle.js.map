{"version": 3, "file": "FileHandle.js", "sourceRoot": "", "sources": ["../../src/node/FileHandle.ts"], "names": [], "mappings": ";;;AAAA,iCAAmC;AACnC,kDAAqD;AAKrD,MAAa,UAAW,SAAQ,qBAAY;IAW1C,YAAY,EAAiB,EAAE,EAAU;QACvC,KAAK,EAAE,CAAC;QAVF,SAAI,GAAW,CAAC,CAAC;QACjB,iBAAY,GAAyB,IAAI,CAAC;QAG1C,aAAQ,GAAW,CAAC,CAAC;QACrB,4BAAuB,GAAY,KAAK,CAAC;QAM/C,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;QACb,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;IACf,CAAC;IAED,UAAU;QACR,gDAAgD;QAChD,4EAA4E;QAC5E,OAAO,IAAI,CAAC,EAAE,CAAC;IACjB,CAAC;IAED,UAAU,CAAC,IAAW,EAAE,OAA0C;QAChE,OAAO,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,YAAY,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;IAClE,CAAC;IAED,KAAK,CAAC,IAAW;QACf,OAAO,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;IACrD,CAAC;IAED,KAAK,CAAC,GAAW,EAAE,GAAW;QAC5B,OAAO,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;IACzD,CAAC;IAED,KAAK;QACH,IAAI,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC;YACnB,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;QAC3B,CAAC;QAED,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACtB,OAAO,IAAI,CAAC,YAAY,CAAC;QAC3B,CAAC;QAED,IAAI,CAAC,IAAI,EAAE,CAAC;QACZ,IAAI,IAAI,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;YACpB,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,CAAC;YAC1B,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;YACb,IAAI,CAAC,YAAY,GAAG,IAAA,gBAAS,EAC3B,IAAI,CAAC,EAAE,EACP,OAAO,CACR,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE;gBACxB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;YAC3B,CAAC,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,YAAY,GAAG,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gBACxD,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC;gBAC5B,IAAI,CAAC,WAAW,GAAG,MAAM,CAAC;YAC5B,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE;gBACd,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;gBACzB,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC;gBAC7B,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC;YAChC,CAAC,CAAC,CAAC;QACL,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACnB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC3B,CAAC;IAED,QAAQ;QACN,OAAO,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,WAAW,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAClD,CAAC;IAED,gBAAgB,CAAC,OAA0C;QACzD,OAAO,IAAI,CAAC,EAAE,CAAC,gBAAgB,CAAC,EAAE,EAAE,EAAE,GAAG,OAAO,EAAE,EAAE,EAAE,IAAI,EAAE,CAAC,CAAC;IAChE,CAAC;IAED,iBAAiB,CAAC,OAA2C;QAC3D,OAAO,IAAI,CAAC,EAAE,CAAC,iBAAiB,CAAC,EAAE,EAAE,EAAE,GAAG,OAAO,EAAE,EAAE,EAAE,IAAI,EAAE,CAAC,CAAC;IACjE,CAAC;IAED,iBAAiB,CAAC,UAA0C,EAAE;QAC5D,MAAM,EAAE,IAAI,GAAG,OAAO,EAAE,SAAS,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;QACtD,IAAI,QAAQ,GAAG,CAAC,CAAC;QAEjB,IAAI,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC;YACnB,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACtB,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;QAC/C,CAAC;QAED,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAC;YACjC,MAAM,IAAI,KAAK,CACb,yHAAyH,CAC1H,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;QACpC,IAAI,CAAC,GAAG,EAAE,CAAC;QAEX,MAAM,gBAAgB,GAAG,GAAG,EAAE;YAC5B,IAAI,CAAC,uBAAuB,GAAG,KAAK,CAAC;YACrC,IAAI,CAAC,KAAK,EAAE,CAAC;YACb,IAAI,SAAS,EAAE,CAAC;gBACd,IAAI,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,GAAG,EAAE;oBACtB,iCAAiC;gBACnC,CAAC,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC;QAEF,OAAO,IAAI,cAAc,CAAC;YACxB,IAAI,EAAE,IAAI,KAAK,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS;YAC5C,qBAAqB,EAAE,KAAK;YAE5B,IAAI,EAAE,KAAK,EAAE,UAAe,EAAE,EAAE;gBAC9B,IAAI,CAAC;oBACH,MAAM,IAAI,GAAG,UAAU,CAAC,WAAW,EAAE,IAAI,CAAC;oBAC1C,IAAI,CAAC,IAAI,EAAE,CAAC;wBACV,0CAA0C;wBAC1C,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC;wBACrC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;wBAEnE,IAAI,MAAM,CAAC,SAAS,KAAK,CAAC,EAAE,CAAC;4BAC3B,UAAU,CAAC,KAAK,EAAE,CAAC;4BACnB,gBAAgB,EAAE,CAAC;4BACnB,OAAO;wBACT,CAAC;wBAED,QAAQ,IAAI,MAAM,CAAC,SAAS,CAAC;wBAC7B,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC;wBACtD,OAAO;oBACT,CAAC;oBAED,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,IAAkB,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;oBAE/F,IAAI,MAAM,CAAC,SAAS,KAAK,CAAC,EAAE,CAAC;wBAC3B,UAAU,CAAC,KAAK,EAAE,CAAC;wBACnB,gBAAgB,EAAE,CAAC;wBACnB,OAAO;oBACT,CAAC;oBAED,QAAQ,IAAI,MAAM,CAAC,SAAS,CAAC;oBAC7B,UAAU,CAAC,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;gBACnD,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;oBACxB,gBAAgB,EAAE,CAAC;gBACrB,CAAC;YACH,CAAC;YAED,MAAM,EAAE,KAAK,IAAI,EAAE;gBACjB,gBAAgB,EAAE,CAAC;YACrB,CAAC;SACF,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,IAAI,CACR,MAA2B,EAC3B,MAAc,EACd,MAAc,EACd,QAAwB;QAExB,MAAM,YAAY,GAAG,QAAQ,KAAK,IAAI,IAAI,QAAQ,KAAK,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC;QAE5F,MAAM,MAAM,GAAG,MAAM,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,CAAC,CACnF,IAAI,CAAC,EAAE,EACP,MAAM,EACN,MAAM,EACN,MAAM,EACN,YAAY,CACb,CAAC;QAEF,+DAA+D;QAC/D,IAAI,QAAQ,KAAK,IAAI,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;YAChD,IAAI,CAAC,QAAQ,IAAI,MAAM,CAAC,SAAS,CAAC;QACpC,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,KAAK,CAAC,OAA0B,EAAE,QAAoC;QACpE,OAAO,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,OAAO,EAAE,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;IACxG,CAAC;IAED,QAAQ,CAAC,OAAwC;QAC/C,OAAO,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;IAC1D,CAAC;IAED,IAAI,CAAC,OAA4B;QAC/B,OAAO,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;IACvD,CAAC;IAED,IAAI;QACF,OAAO,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAC9C,CAAC;IAED,QAAQ,CAAC,GAAY;QACnB,OAAO,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,WAAW,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;IACvD,CAAC;IAED,MAAM,CAAC,KAAY,EAAE,KAAY;QAC/B,OAAO,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;IAC9D,CAAC;IAED,KAAK,CAAC,KAAK,CACT,MAA2B,EAC3B,MAAe,EACf,MAAe,EACf,QAAwB;QAExB,MAAM,mBAAmB,GAAG,OAAO,QAAQ,KAAK,QAAQ,CAAC;QACzD,MAAM,aAAa,GAAW,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC;QAE7E,MAAM,MAAM,GAAG,MAAM,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,OAAO,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC,EAAE,YAAY,EAAE,MAAM,EAAE,CAAC,CAAC,CAC1F,IAAI,CAAC,EAAE,EACP,MAAM,EACN,MAAM,EACN,MAAM,EACN,aAAa,CACd,CAAC;QAEF,+DAA+D;QAC/D,IAAI,mBAAmB,EAAE,CAAC;YACxB,IAAI,CAAC,QAAQ,IAAI,MAAM,CAAC,YAAY,CAAC;QACvC,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,OAA0B,EAAE,QAAoC;QACrE,OAAO,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,QAAQ,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC,EAAE,YAAY,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;IAC/G,CAAC;IAED,SAAS,CAAC,IAAW,EAAE,OAAgC;QACrD,OAAO,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,WAAW,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;IACjE,CAAC;IAED,uDAAuD;IACvD,KAAK,CAAC,CAAE,MAAc,CAAC,YAAY,CAAC;QAClC,MAAM,IAAI,CAAC,KAAK,EAAE,CAAC;IACrB,CAAC;IAEO,GAAG;QACT,IAAI,CAAC,IAAI,EAAE,CAAC;IACd,CAAC;IAEO,KAAK;QACX,IAAI,CAAC,IAAI,EAAE,CAAC;QACZ,IAAI,IAAI,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;YACpB,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;YACb,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBACtB,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;YACjF,CAAC;QACH,CAAC;IACH,CAAC;CACF;AAhQD,gCAgQC"}
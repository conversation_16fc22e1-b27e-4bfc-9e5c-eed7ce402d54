const set = require('regenerate')(0x38B, 0x38D, 0x3A2, 0x530, 0x590, 0x70E, 0x83F, 0x85F, 0x984, 0x9A9, 0x9B1, 0x9DE, 0xA04, 0xA29, 0xA31, 0xA34, 0xA37, 0xA3D, 0xA5D, 0xA84, 0xA8E, 0xA92, 0xAA9, 0xAB1, 0xAB4, 0xAC6, 0xACA, 0xB00, 0xB04, 0xB29, 0xB31, 0xB34, 0xB5E, 0xB84, 0xB91, 0xB9B, 0xB9D, 0xBC9, 0xC0D, 0xC11, 0xC29, 0xC45, 0xC49, 0xC57, 0xC5B, 0xC8D, 0xC91, 0xCA9, 0xCB4, 0xCC5, 0xCC9, 0xCDF, 0xCF0, 0xD0D, 0xD11, 0xD45, 0xD49, 0xD80, 0xD84, 0xDB2, 0xDBC, 0xDD5, 0xDD7, 0xE83, 0xE85, 0xE8B, 0xEA4, 0xEA6, 0xEC5, 0xEC7, 0xECF, 0xF48, 0xF98, 0xFBD, 0xFCD, 0x10C6, 0x1249, 0x1257, 0x1259, 0x1289, 0x12B1, 0x12BF, 0x12C1, 0x12D7, 0x1311, 0x176D, 0x1771, 0x191F, 0x1A5F, 0x1B4D, 0x1F58, 0x1F5A, 0x1F5C, 0x1F5E, 0x1FB5, 0x1FC5, 0x1FDC, 0x1FF5, 0x1FFF, 0x2065, 0x208F, 0x2D26, 0x2DA7, 0x2DAF, 0x2DB7, 0x2DBF, 0x2DC7, 0x2DCF, 0x2DD7, 0x2DDF, 0x2E9A, 0x3040, 0x3130, 0x318F, 0x321F, 0xA9CE, 0xA9FF, 0xAB27, 0xAB2F, 0xFB37, 0xFB3D, 0xFB3F, 0xFB42, 0xFB45, 0xFE53, 0xFE67, 0xFE75, 0xFF00, 0xFFE7, 0x1000C, 0x10027, 0x1003B, 0x1003E, 0x1018F, 0x1039E, 0x1057B, 0x1058B, 0x10593, 0x10596, 0x105A2, 0x105B2, 0x105BA, 0x10786, 0x107B1, 0x10809, 0x10836, 0x10856, 0x108F3, 0x10A04, 0x10A14, 0x10A18, 0x10E7F, 0x10EAA, 0x11135, 0x111E0, 0x11212, 0x11287, 0x11289, 0x1128E, 0x1129E, 0x11304, 0x11329, 0x11331, 0x11334, 0x1133A, 0x1138A, 0x1138F, 0x113B6, 0x113C1, 0x113C6, 0x113CB, 0x113D6, 0x1145C, 0x11914, 0x11917, 0x11936, 0x11C09, 0x11C37, 0x11CA8, 0x11D07, 0x11D0A, 0x11D3B, 0x11D3E, 0x11D66, 0x11D69, 0x11D8F, 0x11D92, 0x11F11, 0x1246F, 0x16A5F, 0x16ABF, 0x16B5A, 0x16B62, 0x1AFF4, 0x1AFFC, 0x1AFFF, 0x1D455, 0x1D49D, 0x1D4AD, 0x1D4BA, 0x1D4BC, 0x1D4C4, 0x1D506, 0x1D515, 0x1D51D, 0x1D53A, 0x1D53F, 0x1D545, 0x1D551, 0x1DAA0, 0x1E007, 0x1E022, 0x1E025, 0x1E6DF, 0x1E7E7, 0x1E7EC, 0x1E7EF, 0x1E7FF, 0x1EE04, 0x1EE20, 0x1EE23, 0x1EE28, 0x1EE33, 0x1EE38, 0x1EE3A, 0x1EE48, 0x1EE4A, 0x1EE4C, 0x1EE50, 0x1EE53, 0x1EE58, 0x1EE5A, 0x1EE5C, 0x1EE5E, 0x1EE60, 0x1EE63, 0x1EE6B, 0x1EE73, 0x1EE78, 0x1EE7D, 0x1EE7F, 0x1EE8A, 0x1EEA4, 0x1EEAA, 0x1F0C0, 0x1F0D0, 0x1FAC7, 0x1FB93);
set.addRange(0x378, 0x379).addRange(0x380, 0x383).addRange(0x557, 0x558).addRange(0x58B, 0x58C).addRange(0x5C8, 0x5CF).addRange(0x5EB, 0x5EE).addRange(0x5F5, 0x5FF).addRange(0x74B, 0x74C).addRange(0x7B2, 0x7BF).addRange(0x7FB, 0x7FC).addRange(0x82E, 0x82F).addRange(0x85C, 0x85D).addRange(0x86B, 0x86F).addRange(0x892, 0x896).addRange(0x98D, 0x98E).addRange(0x991, 0x992).addRange(0x9B3, 0x9B5).addRange(0x9BA, 0x9BB).addRange(0x9C5, 0x9C6).addRange(0x9C9, 0x9CA).addRange(0x9CF, 0x9D6).addRange(0x9D8, 0x9DB).addRange(0x9E4, 0x9E5).addRange(0x9FF, 0xA00).addRange(0xA0B, 0xA0E).addRange(0xA11, 0xA12).addRange(0xA3A, 0xA3B).addRange(0xA43, 0xA46).addRange(0xA49, 0xA4A).addRange(0xA4E, 0xA50).addRange(0xA52, 0xA58).addRange(0xA5F, 0xA65).addRange(0xA77, 0xA80).addRange(0xABA, 0xABB).addRange(0xACE, 0xACF).addRange(0xAD1, 0xADF).addRange(0xAE4, 0xAE5).addRange(0xAF2, 0xAF8).addRange(0xB0D, 0xB0E).addRange(0xB11, 0xB12).addRange(0xB3A, 0xB3B).addRange(0xB45, 0xB46).addRange(0xB49, 0xB4A).addRange(0xB4E, 0xB54).addRange(0xB58, 0xB5B).addRange(0xB64, 0xB65).addRange(0xB78, 0xB81).addRange(0xB8B, 0xB8D).addRange(0xB96, 0xB98).addRange(0xBA0, 0xBA2).addRange(0xBA5, 0xBA7);
set.addRange(0xBAB, 0xBAD).addRange(0xBBA, 0xBBD).addRange(0xBC3, 0xBC5).addRange(0xBCE, 0xBCF).addRange(0xBD1, 0xBD6).addRange(0xBD8, 0xBE5).addRange(0xBFB, 0xBFF).addRange(0xC3A, 0xC3B).addRange(0xC4E, 0xC54).addRange(0xC5E, 0xC5F).addRange(0xC64, 0xC65).addRange(0xC70, 0xC76).addRange(0xCBA, 0xCBB).addRange(0xCCE, 0xCD4).addRange(0xCD7, 0xCDB).addRange(0xCE4, 0xCE5).addRange(0xCF4, 0xCFF).addRange(0xD50, 0xD53).addRange(0xD64, 0xD65).addRange(0xD97, 0xD99).addRange(0xDBE, 0xDBF).addRange(0xDC7, 0xDC9).addRange(0xDCB, 0xDCE).addRange(0xDE0, 0xDE5).addRange(0xDF0, 0xDF1).addRange(0xDF5, 0xE00).addRange(0xE3B, 0xE3E).addRange(0xE5C, 0xE80).addRange(0xEBE, 0xEBF).addRange(0xEDA, 0xEDB).addRange(0xEE0, 0xEFF).addRange(0xF6D, 0xF70).addRange(0xFDB, 0xFFF).addRange(0x10C8, 0x10CC).addRange(0x10CE, 0x10CF).addRange(0x124E, 0x124F).addRange(0x125E, 0x125F).addRange(0x128E, 0x128F).addRange(0x12B6, 0x12B7).addRange(0x12C6, 0x12C7).addRange(0x1316, 0x1317).addRange(0x135B, 0x135C).addRange(0x137D, 0x137F).addRange(0x139A, 0x139F).addRange(0x13F6, 0x13F7).addRange(0x13FE, 0x13FF).addRange(0x169D, 0x169F).addRange(0x16F9, 0x16FF).addRange(0x1716, 0x171E).addRange(0x1737, 0x173F).addRange(0x1754, 0x175F);
set.addRange(0x1774, 0x177F).addRange(0x17DE, 0x17DF).addRange(0x17EA, 0x17EF).addRange(0x17FA, 0x17FF).addRange(0x181A, 0x181F).addRange(0x1879, 0x187F).addRange(0x18AB, 0x18AF).addRange(0x18F6, 0x18FF).addRange(0x192C, 0x192F).addRange(0x193C, 0x193F).addRange(0x1941, 0x1943).addRange(0x196E, 0x196F).addRange(0x1975, 0x197F).addRange(0x19AC, 0x19AF).addRange(0x19CA, 0x19CF).addRange(0x19DB, 0x19DD).addRange(0x1A1C, 0x1A1D).addRange(0x1A7D, 0x1A7E).addRange(0x1A8A, 0x1A8F).addRange(0x1A9A, 0x1A9F).addRange(0x1AAE, 0x1AAF).addRange(0x1ADE, 0x1ADF).addRange(0x1AEC, 0x1AFF).addRange(0x1BF4, 0x1BFB).addRange(0x1C38, 0x1C3A).addRange(0x1C4A, 0x1C4C).addRange(0x1C8B, 0x1C8F).addRange(0x1CBB, 0x1CBC).addRange(0x1CC8, 0x1CCF).addRange(0x1CFB, 0x1CFF).addRange(0x1F16, 0x1F17).addRange(0x1F1E, 0x1F1F).addRange(0x1F46, 0x1F47).addRange(0x1F4E, 0x1F4F).addRange(0x1F7E, 0x1F7F).addRange(0x1FD4, 0x1FD5).addRange(0x1FF0, 0x1FF1).addRange(0x2072, 0x2073).addRange(0x209D, 0x209F).addRange(0x20C2, 0x20CF).addRange(0x20F1, 0x20FF).addRange(0x218C, 0x218F).addRange(0x242A, 0x243F).addRange(0x244B, 0x245F).addRange(0x2B74, 0x2B75).addRange(0x2CF4, 0x2CF8).addRange(0x2D28, 0x2D2C).addRange(0x2D2E, 0x2D2F).addRange(0x2D68, 0x2D6E).addRange(0x2D71, 0x2D7E).addRange(0x2D97, 0x2D9F);
set.addRange(0x2E5E, 0x2E7F).addRange(0x2EF4, 0x2EFF).addRange(0x2FD6, 0x2FEF).addRange(0x3097, 0x3098).addRange(0x3100, 0x3104).addRange(0x31E6, 0x31EE).addRange(0xA48D, 0xA48F).addRange(0xA4C7, 0xA4CF).addRange(0xA62C, 0xA63F).addRange(0xA6F8, 0xA6FF).addRange(0xA7DD, 0xA7F0).addRange(0xA82D, 0xA82F).addRange(0xA83A, 0xA83F).addRange(0xA878, 0xA87F).addRange(0xA8C6, 0xA8CD).addRange(0xA8DA, 0xA8DF).addRange(0xA954, 0xA95E).addRange(0xA97D, 0xA97F).addRange(0xA9DA, 0xA9DD).addRange(0xAA37, 0xAA3F).addRange(0xAA4E, 0xAA4F).addRange(0xAA5A, 0xAA5B).addRange(0xAAC3, 0xAADA).addRange(0xAAF7, 0xAB00).addRange(0xAB07, 0xAB08).addRange(0xAB0F, 0xAB10).addRange(0xAB17, 0xAB1F).addRange(0xAB6C, 0xAB6F).addRange(0xABEE, 0xABEF).addRange(0xABFA, 0xABFF).addRange(0xD7A4, 0xD7AF).addRange(0xD7C7, 0xD7CA).addRange(0xD7FC, 0xF8FF).addRange(0xFA6E, 0xFA6F).addRange(0xFADA, 0xFAFF).addRange(0xFB07, 0xFB12).addRange(0xFB18, 0xFB1C).addRange(0xFDD0, 0xFDEF).addRange(0xFE1A, 0xFE1F).addRange(0xFE6C, 0xFE6F).addRange(0xFEFD, 0xFEFE).addRange(0xFFBF, 0xFFC1).addRange(0xFFC8, 0xFFC9).addRange(0xFFD0, 0xFFD1).addRange(0xFFD8, 0xFFD9).addRange(0xFFDD, 0xFFDF).addRange(0xFFEF, 0xFFF8).addRange(0xFFFE, 0xFFFF).addRange(0x1004E, 0x1004F).addRange(0x1005E, 0x1007F).addRange(0x100FB, 0x100FF);
set.addRange(0x10103, 0x10106).addRange(0x10134, 0x10136).addRange(0x1019D, 0x1019F).addRange(0x101A1, 0x101CF).addRange(0x101FE, 0x1027F).addRange(0x1029D, 0x1029F).addRange(0x102D1, 0x102DF).addRange(0x102FC, 0x102FF).addRange(0x10324, 0x1032C).addRange(0x1034B, 0x1034F).addRange(0x1037B, 0x1037F).addRange(0x103C4, 0x103C7).addRange(0x103D6, 0x103FF).addRange(0x1049E, 0x1049F).addRange(0x104AA, 0x104AF).addRange(0x104D4, 0x104D7).addRange(0x104FC, 0x104FF).addRange(0x10528, 0x1052F).addRange(0x10564, 0x1056E).addRange(0x105BD, 0x105BF).addRange(0x105F4, 0x105FF).addRange(0x10737, 0x1073F).addRange(0x10756, 0x1075F).addRange(0x10768, 0x1077F).addRange(0x107BB, 0x107FF).addRange(0x10806, 0x10807).addRange(0x10839, 0x1083B).addRange(0x1083D, 0x1083E).addRange(0x1089F, 0x108A6).addRange(0x108B0, 0x108DF).addRange(0x108F6, 0x108FA).addRange(0x1091C, 0x1091E).addRange(0x1093A, 0x1093E).addRange(0x1095A, 0x1097F).addRange(0x109B8, 0x109BB).addRange(0x109D0, 0x109D1).addRange(0x10A07, 0x10A0B).addRange(0x10A36, 0x10A37).addRange(0x10A3B, 0x10A3E).addRange(0x10A49, 0x10A4F).addRange(0x10A59, 0x10A5F).addRange(0x10AA0, 0x10ABF).addRange(0x10AE7, 0x10AEA).addRange(0x10AF7, 0x10AFF).addRange(0x10B36, 0x10B38).addRange(0x10B56, 0x10B57).addRange(0x10B73, 0x10B77).addRange(0x10B92, 0x10B98).addRange(0x10B9D, 0x10BA8).addRange(0x10BB0, 0x10BFF).addRange(0x10C49, 0x10C7F);
set.addRange(0x10CB3, 0x10CBF).addRange(0x10CF3, 0x10CF9).addRange(0x10D28, 0x10D2F).addRange(0x10D3A, 0x10D3F).addRange(0x10D66, 0x10D68).addRange(0x10D86, 0x10D8D).addRange(0x10D90, 0x10E5F).addRange(0x10EAE, 0x10EAF).addRange(0x10EB2, 0x10EC1).addRange(0x10EC8, 0x10ECF).addRange(0x10ED9, 0x10EF9).addRange(0x10F28, 0x10F2F).addRange(0x10F5A, 0x10F6F).addRange(0x10F8A, 0x10FAF).addRange(0x10FCC, 0x10FDF).addRange(0x10FF7, 0x10FFF).addRange(0x1104E, 0x11051).addRange(0x11076, 0x1107E).addRange(0x110C3, 0x110CC).addRange(0x110CE, 0x110CF).addRange(0x110E9, 0x110EF).addRange(0x110FA, 0x110FF).addRange(0x11148, 0x1114F).addRange(0x11177, 0x1117F).addRange(0x111F5, 0x111FF).addRange(0x11242, 0x1127F).addRange(0x112AA, 0x112AF).addRange(0x112EB, 0x112EF).addRange(0x112FA, 0x112FF).addRange(0x1130D, 0x1130E).addRange(0x11311, 0x11312).addRange(0x11345, 0x11346).addRange(0x11349, 0x1134A).addRange(0x1134E, 0x1134F).addRange(0x11351, 0x11356).addRange(0x11358, 0x1135C).addRange(0x11364, 0x11365).addRange(0x1136D, 0x1136F).addRange(0x11375, 0x1137F).addRange(0x1138C, 0x1138D).addRange(0x113C3, 0x113C4).addRange(0x113D9, 0x113E0).addRange(0x113E3, 0x113FF).addRange(0x11462, 0x1147F).addRange(0x114C8, 0x114CF).addRange(0x114DA, 0x1157F).addRange(0x115B6, 0x115B7).addRange(0x115DE, 0x115FF).addRange(0x11645, 0x1164F).addRange(0x1165A, 0x1165F).addRange(0x1166D, 0x1167F);
set.addRange(0x116BA, 0x116BF).addRange(0x116CA, 0x116CF).addRange(0x116E4, 0x116FF).addRange(0x1171B, 0x1171C).addRange(0x1172C, 0x1172F).addRange(0x11747, 0x117FF).addRange(0x1183C, 0x1189F).addRange(0x118F3, 0x118FE).addRange(0x11907, 0x11908).addRange(0x1190A, 0x1190B).addRange(0x11939, 0x1193A).addRange(0x11947, 0x1194F).addRange(0x1195A, 0x1199F).addRange(0x119A8, 0x119A9).addRange(0x119D8, 0x119D9).addRange(0x119E5, 0x119FF).addRange(0x11A48, 0x11A4F).addRange(0x11AA3, 0x11AAF).addRange(0x11AF9, 0x11AFF).addRange(0x11B0A, 0x11B5F).addRange(0x11B68, 0x11BBF).addRange(0x11BE2, 0x11BEF).addRange(0x11BFA, 0x11BFF).addRange(0x11C46, 0x11C4F).addRange(0x11C6D, 0x11C6F).addRange(0x11C90, 0x11C91).addRange(0x11CB7, 0x11CFF).addRange(0x11D37, 0x11D39).addRange(0x11D48, 0x11D4F).addRange(0x11D5A, 0x11D5F).addRange(0x11D99, 0x11D9F).addRange(0x11DAA, 0x11DAF).addRange(0x11DDC, 0x11DDF).addRange(0x11DEA, 0x11EDF).addRange(0x11EF9, 0x11EFF).addRange(0x11F3B, 0x11F3D).addRange(0x11F5B, 0x11FAF).addRange(0x11FB1, 0x11FBF).addRange(0x11FF2, 0x11FFE).addRange(0x1239A, 0x123FF).addRange(0x12475, 0x1247F).addRange(0x12544, 0x12F8F).addRange(0x12FF3, 0x12FFF).addRange(0x13456, 0x1345F).addRange(0x143FB, 0x143FF).addRange(0x14647, 0x160FF).addRange(0x1613A, 0x167FF).addRange(0x16A39, 0x16A3F).addRange(0x16A6A, 0x16A6D).addRange(0x16ACA, 0x16ACF).addRange(0x16AEE, 0x16AEF);
set.addRange(0x16AF6, 0x16AFF).addRange(0x16B46, 0x16B4F).addRange(0x16B78, 0x16B7C).addRange(0x16B90, 0x16D3F).addRange(0x16D7A, 0x16E3F).addRange(0x16E9B, 0x16E9F).addRange(0x16EB9, 0x16EBA).addRange(0x16ED4, 0x16EFF).addRange(0x16F4B, 0x16F4E).addRange(0x16F88, 0x16F8E).addRange(0x16FA0, 0x16FDF).addRange(0x16FE5, 0x16FEF).addRange(0x16FF7, 0x16FFF).addRange(0x18CD6, 0x18CFE).addRange(0x18D1F, 0x18D7F).addRange(0x18DF3, 0x1AFEF).addRange(0x1B123, 0x1B131).addRange(0x1B133, 0x1B14F).addRange(0x1B153, 0x1B154).addRange(0x1B156, 0x1B163).addRange(0x1B168, 0x1B16F).addRange(0x1B2FC, 0x1BBFF).addRange(0x1BC6B, 0x1BC6F).addRange(0x1BC7D, 0x1BC7F).addRange(0x1BC89, 0x1BC8F).addRange(0x1BC9A, 0x1BC9B).addRange(0x1BCA4, 0x1CBFF).addRange(0x1CCFD, 0x1CCFF).addRange(0x1CEB4, 0x1CEB9).addRange(0x1CED1, 0x1CEDF).addRange(0x1CEF1, 0x1CEFF).addRange(0x1CF2E, 0x1CF2F).addRange(0x1CF47, 0x1CF4F).addRange(0x1CFC4, 0x1CFFF).addRange(0x1D0F6, 0x1D0FF).addRange(0x1D127, 0x1D128).addRange(0x1D1EB, 0x1D1FF).addRange(0x1D246, 0x1D2BF).addRange(0x1D2D4, 0x1D2DF).addRange(0x1D2F4, 0x1D2FF).addRange(0x1D357, 0x1D35F).addRange(0x1D379, 0x1D3FF).addRange(0x1D4A0, 0x1D4A1).addRange(0x1D4A3, 0x1D4A4).addRange(0x1D4A7, 0x1D4A8).addRange(0x1D50B, 0x1D50C).addRange(0x1D547, 0x1D549).addRange(0x1D6A6, 0x1D6A7).addRange(0x1D7CC, 0x1D7CD).addRange(0x1DA8C, 0x1DA9A).addRange(0x1DAB0, 0x1DEFF);
set.addRange(0x1DF1F, 0x1DF24).addRange(0x1DF2B, 0x1DFFF).addRange(0x1E019, 0x1E01A).addRange(0x1E02B, 0x1E02F).addRange(0x1E06E, 0x1E08E).addRange(0x1E090, 0x1E0FF).addRange(0x1E12D, 0x1E12F).addRange(0x1E13E, 0x1E13F).addRange(0x1E14A, 0x1E14D).addRange(0x1E150, 0x1E28F).addRange(0x1E2AF, 0x1E2BF).addRange(0x1E2FA, 0x1E2FE).addRange(0x1E300, 0x1E4CF).addRange(0x1E4FA, 0x1E5CF).addRange(0x1E5FB, 0x1E5FE).addRange(0x1E600, 0x1E6BF).addRange(0x1E6F6, 0x1E6FD).addRange(0x1E700, 0x1E7DF).addRange(0x1E8C5, 0x1E8C6).addRange(0x1E8D7, 0x1E8FF).addRange(0x1E94C, 0x1E94F).addRange(0x1E95A, 0x1E95D).addRange(0x1E960, 0x1EC70).addRange(0x1ECB5, 0x1ED00).addRange(0x1ED3E, 0x1EDFF).addRange(0x1EE25, 0x1EE26).addRange(0x1EE3C, 0x1EE41).addRange(0x1EE43, 0x1EE46).addRange(0x1EE55, 0x1EE56).addRange(0x1EE65, 0x1EE66).addRange(0x1EE9C, 0x1EEA0).addRange(0x1EEBC, 0x1EEEF).addRange(0x1EEF2, 0x1EFFF).addRange(0x1F02C, 0x1F02F).addRange(0x1F094, 0x1F09F).addRange(0x1F0AF, 0x1F0B0).addRange(0x1F0F6, 0x1F0FF).addRange(0x1F1AE, 0x1F1E5).addRange(0x1F203, 0x1F20F).addRange(0x1F23C, 0x1F23F).addRange(0x1F249, 0x1F24F).addRange(0x1F252, 0x1F25F).addRange(0x1F266, 0x1F2FF).addRange(0x1F6D9, 0x1F6DB).addRange(0x1F6ED, 0x1F6EF).addRange(0x1F6FD, 0x1F6FF).addRange(0x1F7DA, 0x1F7DF).addRange(0x1F7EC, 0x1F7EF).addRange(0x1F7F1, 0x1F7FF).addRange(0x1F80C, 0x1F80F).addRange(0x1F848, 0x1F84F);
set.addRange(0x1F85A, 0x1F85F).addRange(0x1F888, 0x1F88F).addRange(0x1F8AE, 0x1F8AF).addRange(0x1F8BC, 0x1F8BF).addRange(0x1F8C2, 0x1F8CF).addRange(0x1F8D9, 0x1F8FF).addRange(0x1FA58, 0x1FA5F).addRange(0x1FA6E, 0x1FA6F).addRange(0x1FA7D, 0x1FA7F).addRange(0x1FA8B, 0x1FA8D).addRange(0x1FAC9, 0x1FACC).addRange(0x1FADD, 0x1FADE).addRange(0x1FAEB, 0x1FAEE).addRange(0x1FAF9, 0x1FAFF).addRange(0x1FBFB, 0x1FFFF).addRange(0x2A6E0, 0x2A6FF).addRange(0x2B81E, 0x2B81F).addRange(0x2CEAE, 0x2CEAF).addRange(0x2EBE1, 0x2EBEF).addRange(0x2EE5E, 0x2F7FF).addRange(0x2FA1E, 0x2FFFF).addRange(0x3134B, 0x3134F).addRange(0x3347A, 0xE0000).addRange(0xE0002, 0xE001F).addRange(0xE0080, 0xE00FF).addRange(0xE01F0, 0x10FFFF);
exports.characters = set;

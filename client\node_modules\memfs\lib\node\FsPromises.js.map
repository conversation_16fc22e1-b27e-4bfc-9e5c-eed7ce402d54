{"version": 3, "file": "FsPromises.js", "sourceRoot": "", "sources": ["../../src/node/FsPromises.ts"], "names": [], "mappings": ";;;AAAA,iCAAqE;AACrE,4CAAyC;AAKzC,kDAAkD;AAClD,MAAM,oBAAoB;IASxB,YACU,EAAO,EACP,IAAmB,EACnB,UAA8B,EAAE;QAFhC,OAAE,GAAF,EAAE,CAAK;QACP,SAAI,GAAJ,IAAI,CAAe;QACnB,YAAO,GAAP,OAAO,CAAyB;QAVlC,eAAU,GAA4D,EAAE,CAAC;QACzE,iBAAY,GAAmD,EAAE,CAAC;QAClE,aAAQ,GAAG,KAAK,CAAC;QAUvB,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,IAAI,CAAC;QACzC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,QAAQ,CAAC;QAC7C,IAAI,CAAC,aAAa,EAAE,CAAC;QAErB,qBAAqB;QACrB,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACnB,IAAI,OAAO,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;gBAC3B,IAAI,CAAC,MAAM,EAAE,CAAC;gBACd,OAAO;YACT,CAAC;YACD,OAAO,CAAC,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE;gBAC5C,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,CAAC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAEO,aAAa;QACnB,IAAI,CAAC;YACH,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,SAAiB,EAAE,QAAgB,EAAE,EAAE;gBAC5F,IAAI,CAAC,YAAY,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,CAAC,CAAC;YAC7C,CAAC,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,iDAAiD;YACjD,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,YAAY,CAAC,KAAuD;QAC1E,IAAI,IAAI,CAAC,QAAQ;YAAE,OAAO;QAE1B,wBAAwB;QACxB,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC5C,IAAI,IAAI,CAAC,QAAQ,KAAK,OAAO,EAAE,CAAC;gBAC9B,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,mCAAmC,IAAI,CAAC,QAAQ,gBAAgB,CAAC,CAAC;gBAC1F,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBACnB,OAAO;YACT,CAAC;iBAAM,CAAC;gBACN,mCAAmC;gBACnC,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;gBACxB,OAAO,CAAC,IAAI,CAAC,qEAAqE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;YACrG,CAAC;QACH,CAAC;QAED,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAE5B,2CAA2C;QAC3C,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACjC,MAAM,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,EAAG,CAAC;YAC/C,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,EAAG,CAAC;YAC3C,OAAO,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC;QAC7C,CAAC;IACH,CAAC;IAEO,MAAM,CAAC,KAAa;QAC1B,IAAI,IAAI,CAAC,QAAQ;YAAE,OAAO;QAC1B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QAErB,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;YACrB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACtB,CAAC;QAED,yCAAyC;QACzC,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACpC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,EAAG,CAAC;YACvD,IAAI,KAAK,EAAE,CAAC;gBACV,MAAM,CAAC,KAAK,CAAC,CAAC;YAChB,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;YAC5C,CAAC;QACH,CAAC;IACH,CAAC;IAED,KAAK,CAAC,IAAI;QACR,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;QAC1C,CAAC;QAED,uCAAuC;QACvC,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC/B,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,EAAG,CAAC;YACvC,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;QACvC,CAAC;QAED,qCAAqC;QACrC,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,MAAM;QACV,IAAI,CAAC,MAAM,EAAE,CAAC;QACd,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;IAC1C,CAAC;IAED,KAAK,CAAC,KAAK,CAAC,KAAU;QACpB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QACnB,MAAM,KAAK,CAAC;IACd,CAAC;IAED,CAAC,MAAM,CAAC,aAAa,CAAC;QACpB,OAAO,IAAI,CAAC;IACd,CAAC;CACF;AAED,MAAa,UAAU;IAGrB,YACqB,EAAiB,EACpB,UAAwD;QADrD,OAAE,GAAF,EAAE,CAAe;QACpB,eAAU,GAAV,UAAU,CAA8C;QAJ1D,cAAS,GAAG,qBAAS,CAAC;QAOtB,OAAE,GAAG,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;QAC9B,YAAO,GAAG,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;QACxC,WAAM,GAAG,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;QACtC,YAAO,GAAG,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;QACxC,SAAI,GAAG,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;QAClC,WAAM,GAAG,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;QACtC,UAAK,GAAG,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;QACpC,UAAK,GAAG,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;QACpC,aAAQ,GAAG,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC;QAC1C,WAAM,GAAG,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;QACtC,WAAM,GAAG,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;QACtC,SAAI,GAAG,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;QAClC,UAAK,GAAG,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;QACpC,UAAK,GAAG,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;QACpC,YAAO,GAAG,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;QACxC,YAAO,GAAG,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;QACxC,aAAQ,GAAG,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC;QAC1C,aAAQ,GAAG,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC;QAC1C,WAAM,GAAG,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;QACtC,UAAK,GAAG,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;QACpC,OAAE,GAAG,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;QAC9B,SAAI,GAAG,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;QAClC,YAAO,GAAG,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;QACxC,aAAQ,GAAG,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC;QAC1C,WAAM,GAAG,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;QACtC,WAAM,GAAG,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;QAEtC,aAAQ,GAAG,CACzB,EAAoB,EACpB,OAAwC,EAChB,EAAE;YAC1B,OAAO,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC,EAAE,YAAY,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAE,EAAoB,EAAE,OAAO,CAAC,CAAC;QAChH,CAAC,CAAC;QAEc,eAAU,GAAG,CAC3B,IAAsB,EACtB,IAAgB,EAChB,OAA0C,EAC3B,EAAE;YACjB,OAAO,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,YAAY,CAAC,CACrC,IAAI,YAAY,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAE,IAAsB,EACnE,IAAI,EACJ,OAAO,CACR,CAAC;QACJ,CAAC,CAAC;QAEc,SAAI,GAAG,CAAC,IAAmB,EAAE,QAAqB,GAAG,EAAE,IAAiB,EAAE,EAAE;YAC1F,OAAO,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QAC/F,CAAC,CAAC;QAEc,cAAS,GAAG,CAC1B,EAAoB,EACpB,IAAwB,EACxB,OAAgC,EACjB,EAAE;YACjB,MAAM,WAAW,GAAG,IAAA,uBAAgB,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAA,qBAAc,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAC1F,OAAO,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAC7B,IAAA,gBAAS,EAAC,IAAI,CAAC,EAAE,EAAE,WAAW,CAAC,CAAC,EAAE,YAAY,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAE,EAAoB,EAAE,IAAI,EAAE,OAAO,CAAC,CAC9G,CAAC;QACJ,CAAC,CAAC;QAEc,UAAK,GAAG,CACtB,QAAuB,EACvB,OAAqC,EACoC,EAAE;YAC3E,MAAM,YAAY,GAAuB,OAAO,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,OAAc,EAAE,CAAC,CAAC,CAAC,OAAO,IAAI,EAAE,CAAC;YACpH,OAAO,IAAI,oBAAoB,CAAC,IAAI,CAAC,EAAE,EAAE,QAAQ,EAAE,YAAY,CAAC,CAAC;QACnE,CAAC,CAAC;IArEC,CAAC;CAsEL;AA5ED,gCA4EC"}